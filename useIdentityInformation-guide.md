# useIdentityInformation Hook Usage Guide

## Overview

The `useIdentityInformation` hook provides access to data from the `willBecomeIdentityInformationLoader` function, which includes user cities, status information, and permissions for identity information management.

## Hook Implementation

<augment_code_snippet path="app/hooks/useIdentityInformation.ts" mode="EXCERPT">
````typescript
export const useIdentityInformation = () => {
    return useOutletContext<
        SerializeFrom<typeof willBecomeIdentityInformationLoader>
    >();
};
````
</augment_code_snippet>

## Data Structure

The hook returns an object with the following structure:

```typescript
type IdentityInformationData = {
  userCities: string[];                    // Array of city names the user can access
  status: string[];                        // Array of status values: ['VERIFYING', 'APPROVED']
  permissions: BtaskeePermissions['key'][]; // Array of permission keys
}
```

### Detailed Type Definitions

Based on the codebase analysis:

```typescript
// From btaskee-types
interface BtaskeePermissions {
  _id: string;
  name: string;
  description: string;
  module: string;
  'slug-module': string;
  key: string;
}

// Status values from TASKER_ONBOARDING_PROCESS_STATUS
type StatusValues = 'VERIFYING' | 'APPROVED';

// Permission keys for identity information
type IdentityPermissions = 
  | 'READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER'
  | 'READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER';
```

## Usage Examples

### Basic Usage

```typescript
import { useIdentityInformation } from '~/hooks/useIdentityInformation';

export default function IdentityInformationComponent() {
  const identityData = useIdentityInformation();
  
  // Access the data
  const { userCities, status, permissions } = identityData;
  
  return (
    <div>
      <h2>Identity Information</h2>
      
      {/* Display user cities */}
      <div>
        <h3>Available Cities:</h3>
        <ul>
          {userCities.map((city, index) => (
            <li key={index}>{city}</li>
          ))}
        </ul>
      </div>
      
      {/* Display status options */}
      <div>
        <h3>Status Options:</h3>
        <ul>
          {status.map((statusItem, index) => (
            <li key={index}>{statusItem}</li>
          ))}
        </ul>
      </div>
      
      {/* Display permissions */}
      <div>
        <h3>User Permissions:</h3>
        <ul>
          {permissions.map((permission, index) => (
            <li key={index}>{permission}</li>
          ))}
        </ul>
      </div>
    </div>
  );
}
```

### With TypeScript and Error Handling

```typescript
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import type { SerializeFrom } from '@remix-run/node';
import type { willBecomeIdentityInformationLoader } from '~/hooks/useIdentityInformation';

type IdentityInformationData = SerializeFrom<typeof willBecomeIdentityInformationLoader>;

export default function IdentityInformationComponent() {
  const identityData: IdentityInformationData = useIdentityInformation();
  
  // Destructure with default values for safety
  const { 
    userCities = [], 
    status = [], 
    permissions = [] 
  } = identityData || {};
  
  // Error handling
  if (!identityData) {
    return <div>Loading identity information...</div>;
  }
  
  if (userCities.length === 0) {
    return <div>No cities available for this user.</div>;
  }
  
  return (
    <div className="identity-information">
      <h2>Identity Information Dashboard</h2>
      
      {/* Cities with conditional rendering */}
      {userCities.length > 0 && (
        <section>
          <h3>Accessible Cities ({userCities.length})</h3>
          <div className="cities-grid">
            {userCities.map((city, index) => (
              <div key={index} className="city-card">
                {city}
              </div>
            ))}
          </div>
        </section>
      )}
      
      {/* Status information */}
      {status.length > 0 && (
        <section>
          <h3>Available Status Options</h3>
          <div className="status-list">
            {status.map((statusItem, index) => (
              <span key={index} className={`status-badge status-${statusItem.toLowerCase()}`}>
                {statusItem}
              </span>
            ))}
          </div>
        </section>
      )}
      
      {/* Permissions check */}
      <section>
        <h3>User Permissions</h3>
        <div className="permissions-info">
          <p>Can verify identity information: {
            permissions.includes('READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER') ? 'Yes' : 'No'
          }</p>
          <p>Can view approved identity information: {
            permissions.includes('READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER') ? 'Yes' : 'No'
          }</p>
        </div>
      </section>
    </div>
  );
}
```

### Using with Conditional Logic

```typescript
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import { PERMISSIONS } from 'btaskee-constants';

export default function ConditionalIdentityComponent() {
  const { userCities, status, permissions } = useIdentityInformation();
  
  // Check specific permissions
  const canVerifyIdentity = permissions.includes(
    PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER
  );
  const canViewApproved = permissions.includes(
    PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER
  );
  
  // Filter data based on permissions
  const availableActions = [];
  if (canVerifyIdentity) availableActions.push('Verify Identity');
  if (canViewApproved) availableActions.push('View Approved');
  
  return (
    <div>
      {/* Conditional rendering based on permissions */}
      {canVerifyIdentity && (
        <button onClick={() => console.log('Navigate to verify')}>
          Verify Identity Information
        </button>
      )}
      
      {canViewApproved && (
        <button onClick={() => console.log('Navigate to approved')}>
          View Approved Information
        </button>
      )}
      
      {/* Display filtered cities */}
      <select>
        <option value="">Select a city</option>
        {userCities.map((city, index) => (
          <option key={index} value={city}>
            {city}
          </option>
        ))}
      </select>
      
      {/* Status filter */}
      <select>
        <option value="">All Status</option>
        {status.map((statusItem, index) => (
          <option key={index} value={statusItem}>
            {statusItem}
          </option>
        ))}
      </select>
    </div>
  );
}
```

## Integration with Existing Components

Looking at your current file, you can integrate the hook like this:

```typescript
// In your _index.tsx file
import { useIdentityInformation } from '~/hooks/useIdentityInformation';

export default function IdentityInformation() {
  const { t } = useTranslation('identity-information');
  const [searchParams, setSearchParams] = useSearchParams();
  
  // Get identity information data
  const identityInfo = useIdentityInformation();
  const { userCities, status: availableStatus, permissions } = identityInfo;
  
  // Use the existing outletData for table data
  const outletData = useOutletGetStaffOnboardingProfile();
  
  // Your existing component logic...
  
  return (
    <div className="flex flex-col gap-6">
      {/* Display identity information */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3>Identity Information Context</h3>
        <p>Available Cities: {userCities.join(', ')}</p>
        <p>Status Options: {availableStatus.join(', ')}</p>
        <p>Permissions: {permissions.length} permission(s)</p>
      </div>
      
      {/* Your existing component JSX */}
      <Grid className="p-4 bg-secondary">
        {/* ... rest of your component */}
      </Grid>
    </div>
  );
}
```

## Error Handling Best Practices

```typescript
import { useIdentityInformation } from '~/hooks/useIdentityInformation';

export default function SafeIdentityComponent() {
  try {
    const identityData = useIdentityInformation();
    
    // Validate data structure
    if (!identityData || typeof identityData !== 'object') {
      throw new Error('Invalid identity data structure');
    }
    
    const { userCities, status, permissions } = identityData;
    
    // Validate arrays
    if (!Array.isArray(userCities) || !Array.isArray(status) || !Array.isArray(permissions)) {
      throw new Error('Expected arrays for identity data properties');
    }
    
    return (
      <div>
        {/* Your component JSX */}
      </div>
    );
    
  } catch (error) {
    console.error('Error accessing identity information:', error);
    return (
      <div className="error-state">
        <p>Unable to load identity information. Please try again.</p>
      </div>
    );
  }
}
```

## Key Points to Remember

1. **Hook Context**: The hook uses `useOutletContext`, so it must be used within a component that's a child of the route that provides this context.

2. **Data Types**: 
   - `userCities`: Array of strings (city names)
   - `status`: Array of strings (always contains 'VERIFYING' and 'APPROVED')
   - `permissions`: Array of permission key strings

3. **Permission Checking**: Use the `PERMISSIONS` constants from 'btaskee-constants' for reliable permission checking.

4. **Error Handling**: Always provide fallbacks and validate the data structure.

5. **TypeScript**: Use `SerializeFrom<typeof willBecomeIdentityInformationLoader>` for proper typing.

This guide should help you properly access and display the data from the `useIdentityInformation` hook in your React components.
