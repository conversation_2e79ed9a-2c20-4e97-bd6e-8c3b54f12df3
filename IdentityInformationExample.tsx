import React from 'react';
import type { SerializeFrom } from '@remix-run/node';
import { useTranslation } from 'react-i18next';
import { PERMISSIONS } from 'btaskee-constants';
import { Badge, Typography, Card, CardContent, CardHeader } from 'btaskee-ui';
import { useIdentityInformation } from '~/hooks/useIdentityInformation';
import type { willBecomeIdentityInformationLoader } from '~/hooks/useIdentityInformation';

// Type definition for the hook data
type IdentityInformationData = SerializeFrom<typeof willBecomeIdentityInformationLoader>;

/**
 * Example component demonstrating how to use the useIdentityInformation hook
 * This component shows how to access and display userCities, status, and permissions
 */
export default function IdentityInformationExample() {
  const { t } = useTranslation('identity-information');
  
  // Get the identity information data using the hook
  const identityData: IdentityInformationData = useIdentityInformation();
  
  // Destructure with default values for safety
  const { 
    userCities = [], 
    status = [], 
    permissions = [] 
  } = identityData || {};

  // Permission checks using constants
  const canVerifyIdentity = permissions.includes(
    PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER
  );
  const canViewApproved = permissions.includes(
    PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER
  );

  // Error handling - if no data is available
  if (!identityData) {
    return (
      <Card>
        <CardContent>
          <Typography variant="p" className="text-gray-500">
            Loading identity information...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Typography variant="h2" className="mb-4">
        Identity Information Dashboard
      </Typography>

      {/* User Cities Section */}
      <Card>
        <CardHeader>
          <Typography variant="h3">
            Accessible Cities ({userCities.length})
          </Typography>
        </CardHeader>
        <CardContent>
          {userCities.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {userCities.map((city, index) => (
                <Badge 
                  key={index} 
                  variant="secondary"
                  className="bg-blue-50 text-blue-700"
                >
                  {city}
                </Badge>
              ))}
            </div>
          ) : (
            <Typography variant="p" className="text-gray-500">
              No cities available for this user.
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Status Information Section */}
      <Card>
        <CardHeader>
          <Typography variant="h3">
            Available Status Options
          </Typography>
        </CardHeader>
        <CardContent>
          {status.length > 0 ? (
            <div className="flex gap-3">
              {status.map((statusItem, index) => (
                <Badge 
                  key={index}
                  variant={statusItem === 'APPROVED' ? 'default' : 'secondary'}
                  className={
                    statusItem === 'APPROVED' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }
                >
                  {t(statusItem)}
                </Badge>
              ))}
            </div>
          ) : (
            <Typography variant="p" className="text-gray-500">
              No status options available.
            </Typography>
          )}
        </CardContent>
      </Card>

      {/* Permissions Section */}
      <Card>
        <CardHeader>
          <Typography variant="h3">
            User Permissions
          </Typography>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <Typography variant="p">
                Can verify identity information:
              </Typography>
              <Badge variant={canVerifyIdentity ? 'default' : 'secondary'}>
                {canVerifyIdentity ? 'Yes' : 'No'}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <Typography variant="p">
                Can view approved identity information:
              </Typography>
              <Badge variant={canViewApproved ? 'default' : 'secondary'}>
                {canViewApproved ? 'Yes' : 'No'}
              </Badge>
            </div>

            {/* Display all permissions */}
            <div className="mt-4">
              <Typography variant="h4" className="mb-2">
                All Permissions ({permissions.length}):
              </Typography>
              {permissions.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {permissions.map((permission, index) => (
                    <Badge 
                      key={index} 
                      variant="outline"
                      className="text-xs"
                    >
                      {permission}
                    </Badge>
                  ))}
                </div>
              ) : (
                <Typography variant="p" className="text-gray-500">
                  No permissions assigned.
                </Typography>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons Based on Permissions */}
      <Card>
        <CardHeader>
          <Typography variant="h3">
            Available Actions
          </Typography>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3">
            {canVerifyIdentity && (
              <button 
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => {
                  console.log('Navigate to verify identity information');
                  // Add your navigation logic here
                }}
              >
                Verify Identity Information
              </button>
            )}
            
            {canViewApproved && (
              <button 
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                onClick={() => {
                  console.log('Navigate to approved identity information');
                  // Add your navigation logic here
                }}
              >
                View Approved Information
              </button>
            )}
            
            {!canVerifyIdentity && !canViewApproved && (
              <Typography variant="p" className="text-gray-500">
                No actions available with current permissions.
              </Typography>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Data Export/Debug Section */}
      <Card>
        <CardHeader>
          <Typography variant="h3">
            Raw Data (for debugging)
          </Typography>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto">
            {JSON.stringify(identityData, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Alternative minimal usage example
 */
export function MinimalIdentityExample() {
  const { userCities, status, permissions } = useIdentityInformation();
  
  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-semibold mb-2">Identity Information Summary</h3>
      <p>Cities: {userCities.join(', ')}</p>
      <p>Status: {status.join(', ')}</p>
      <p>Permissions: {permissions.length} permission(s)</p>
    </div>
  );
}

/**
 * Hook usage in a form context
 */
export function IdentityFormExample() {
  const { userCities, status } = useIdentityInformation();
  
  return (
    <form className="space-y-4">
      {/* City selector */}
      <div>
        <label htmlFor="city" className="block text-sm font-medium mb-1">
          Select City:
        </label>
        <select id="city" className="w-full p-2 border rounded-md">
          <option value="">Choose a city...</option>
          {userCities.map((city, index) => (
            <option key={index} value={city}>
              {city}
            </option>
          ))}
        </select>
      </div>

      {/* Status filter */}
      <div>
        <label htmlFor="status" className="block text-sm font-medium mb-1">
          Filter by Status:
        </label>
        <select id="status" className="w-full p-2 border rounded-md">
          <option value="">All Status</option>
          {status.map((statusItem, index) => (
            <option key={index} value={statusItem}>
              {statusItem}
            </option>
          ))}
        </select>
      </div>
    </form>
  );
}

/**
 * Custom hook for permission checking
 */
export function useIdentityPermissions() {
  const { permissions } = useIdentityInformation();
  
  return {
    canVerifyIdentity: permissions.includes(
      PERMISSIONS.READ_VERIFY_IDENTITY_INFORMATION_FOR_TASKER
    ),
    canViewApproved: permissions.includes(
      PERMISSIONS.READ_APPROVED_IDENTITY_INFORMATION_FOR_TASKER
    ),
    hasAnyPermission: permissions.length > 0,
    allPermissions: permissions
  };
}
